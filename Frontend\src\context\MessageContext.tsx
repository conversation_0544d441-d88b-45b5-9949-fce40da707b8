import React, { createContext, useContext, useState, useEffect } from 'react';
import { useContract } from './ContractContext';

interface Message {
  id: string;
  reportId: number;
  senderId: string;
  senderType: 'user' | 'agent';
  content: string;
  timestamp: number;
  read: boolean;
}

interface MessageThread {
  reportId: number;
  reporterAddress: string;
  agentAddress?: string;
  messages: Message[];
  lastActivity: number;
  unreadCount: number;
}

interface MessageContextType {
  threads: MessageThread[];
  unreadCount: number;
  getThreadByReportId: (reportId: number) => MessageThread | undefined;
  sendMessage: (reportId: number, content: string) => Promise<void>;
  markAsRead: (reportId: number, messageId?: string) => void;
  createThread: (reportId: number, reporterAddress: string, agentAddress?: string) => void;
}

const MessageContext = createContext<MessageContextType | undefined>(undefined);

export const useMessages = () => {
  const context = useContext(MessageContext);
  if (!context) {
    throw new Error('useMessages must be used within a MessageProvider');
  }
  return context;
};

interface MessageProviderProps {
  children: React.ReactNode;
}

export const MessageProvider: React.FC<MessageProviderProps> = ({ children }) => {
  const [threads, setThreads] = useState<MessageThread[]>([]);
  const { account, isAgent } = useContract();

  // Mock data initialization
  useEffect(() => {
    // In a real app, this would fetch from backend/blockchain
    const mockThreads: MessageThread[] = [
      {
        reportId: 1,
        reporterAddress: '******************************************',
        agentAddress: '******************************************',
        lastActivity: Date.now() - 900000, // 15 minutes ago
        unreadCount: isAgent ? 1 : 0,
        messages: [
          {
            id: '1',
            reportId: 1,
            senderId: '******************************************',
            senderType: 'user',
            content: 'Hi! I have some plastic bottles ready for collection.',
            timestamp: Date.now() - 3600000,
            read: true
          },
          {
            id: '2',
            reportId: 1,
            senderId: '******************************************',
            senderType: 'agent',
            content: 'Great! I can come by this afternoon around 2 PM.',
            timestamp: Date.now() - 1800000,
            read: true
          },
          {
            id: '3',
            reportId: 1,
            senderId: '******************************************',
            senderType: 'user',
            content: 'Perfect! I\'ll be home then.',
            timestamp: Date.now() - 900000,
            read: !isAgent
          }
        ]
      }
    ];
    setThreads(mockThreads);
  }, [isAgent]);

  const unreadCount = threads.reduce((total, thread) => {
    if (isAgent) {
      // Agent sees unread messages from users
      return total + thread.messages.filter(msg => 
        msg.senderType === 'user' && !msg.read
      ).length;
    } else {
      // User sees unread messages from agents
      return total + thread.messages.filter(msg => 
        msg.senderType === 'agent' && !msg.read
      ).length;
    }
  }, 0);

  const getThreadByReportId = (reportId: number): MessageThread | undefined => {
    return threads.find(thread => thread.reportId === reportId);
  };

  const sendMessage = async (reportId: number, content: string): Promise<void> => {
    const newMessage: Message = {
      id: Date.now().toString(),
      reportId,
      senderId: account || '',
      senderType: isAgent ? 'agent' : 'user',
      content,
      timestamp: Date.now(),
      read: false
    };

    setThreads(prevThreads => 
      prevThreads.map(thread => {
        if (thread.reportId === reportId) {
          return {
            ...thread,
            messages: [...thread.messages, newMessage],
            lastActivity: Date.now()
          };
        }
        return thread;
      })
    );

    // In real app, send to backend/blockchain here
    // await sendMessageToBlockchain(reportId, newMessage);
  };

  const markAsRead = (reportId: number, messageId?: string): void => {
    setThreads(prevThreads =>
      prevThreads.map(thread => {
        if (thread.reportId === reportId) {
          const updatedMessages = thread.messages.map(msg => {
            if (messageId) {
              // Mark specific message as read
              return msg.id === messageId ? { ...msg, read: true } : msg;
            } else {
              // Mark all messages from the other party as read
              const shouldMarkRead = isAgent ? msg.senderType === 'user' : msg.senderType === 'agent';
              return shouldMarkRead ? { ...msg, read: true } : msg;
            }
          });
          
          return {
            ...thread,
            messages: updatedMessages,
            unreadCount: isAgent 
              ? updatedMessages.filter(msg => msg.senderType === 'user' && !msg.read).length
              : updatedMessages.filter(msg => msg.senderType === 'agent' && !msg.read).length
          };
        }
        return thread;
      })
    );
  };

  const createThread = (reportId: number, reporterAddress: string, agentAddress?: string): void => {
    const existingThread = getThreadByReportId(reportId);
    if (existingThread) return;

    const newThread: MessageThread = {
      reportId,
      reporterAddress,
      agentAddress,
      messages: [],
      lastActivity: Date.now(),
      unreadCount: 0
    };

    setThreads(prevThreads => [...prevThreads, newThread]);
  };

  return (
    <MessageContext.Provider
      value={{
        threads,
        unreadCount,
        getThreadByReportId,
        sendMessage,
        markAsRead,
        createThread
      }}
    >
      {children}
    </MessageContext.Provider>
  );
};
