import React, { useState, useEffect, useRef } from 'react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { EnhancedCard, EnhancedCardHeader, EnhancedCardContent, EnhancedCardFooter } from './ui/enhanced-card';
import { Send, MessageCircle, User, Users, Clock, CheckCheck } from 'lucide-react';
import { useContract } from '../context/ContractContext';
import { toast } from 'sonner';

interface Message {
  id: string;
  senderId: string;
  senderType: 'user' | 'agent';
  content: string;
  timestamp: number;
  read: boolean;
}

interface MessageThreadProps {
  reportId: number;
  reporterAddress: string;
  agentAddress?: string;
  onClose: () => void;
  isAgent?: boolean;
}

const MessageThread: React.FC<MessageThreadProps> = ({
  reportId,
  reporterAddress,
  agentAddress,
  onClose,
  isAgent = false
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { account } = useContract();

  // Mock data for demonstration - in real app, this would come from backend/blockchain
  useEffect(() => {
    // Simulate loading existing messages
    const mockMessages: Message[] = [
      {
        id: '1',
        senderId: reporterAddress,
        senderType: 'user',
        content: 'Hi! I have some plastic bottles ready for collection. When would be a good time?',
        timestamp: Date.now() - 3600000, // 1 hour ago
        read: true
      },
      {
        id: '2',
        senderId: agentAddress || '',
        senderType: 'agent',
        content: 'Hello! I can come by this afternoon around 2 PM. Is that convenient for you?',
        timestamp: Date.now() - 1800000, // 30 minutes ago
        read: true
      },
      {
        id: '3',
        senderId: reporterAddress,
        senderType: 'user',
        content: 'Perfect! I\'ll be home then. The waste is in bags by the front door.',
        timestamp: Date.now() - 900000, // 15 minutes ago
        read: !isAgent
      }
    ];
    setMessages(mockMessages);
  }, [reportId, reporterAddress, agentAddress, isAgent]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = async () => {
    if (!newMessage.trim()) return;

    setIsLoading(true);
    try {
      const message: Message = {
        id: Date.now().toString(),
        senderId: account || '',
        senderType: isAgent ? 'agent' : 'user',
        content: newMessage.trim(),
        timestamp: Date.now(),
        read: false
      };

      setMessages(prev => [...prev, message]);
      setNewMessage('');
      toast.success('Message sent!');

      // In real app, send to backend/blockchain here
      // await sendMessageToBlockchain(reportId, message);

    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const minutes = Math.floor(diffInHours * 60);
      return `${minutes}m ago`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const getOtherPartyAddress = () => {
    return isAgent ? reporterAddress : (agentAddress || 'Agent');
  };

  return (
    <EnhancedCard variant="glass" className="h-[600px] flex flex-col">
      <EnhancedCardHeader 
        icon={<MessageCircle className="h-5 w-5 text-waste-600" />}
        badge={
          <Button variant="ghost" size="sm" onClick={onClose} className="text-gray-500 hover:text-gray-700">
            ✕
          </Button>
        }
      >
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Message Thread - Report #{reportId}
          </h3>
          <div className="flex items-center space-x-2 mt-1">
            {isAgent ? <User className="h-4 w-4" /> : <Users className="h-4 w-4" />}
            <span className="text-sm text-gray-500 dark:text-gray-400">
              Chatting with {getOtherPartyAddress().substring(0, 6)}...{getOtherPartyAddress().substring(getOtherPartyAddress().length - 4)}
            </span>
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
              Online
            </Badge>
          </div>
        </div>
      </EnhancedCardHeader>

      <EnhancedCardContent className="flex-1 overflow-hidden">
        <div className="h-full overflow-y-auto space-y-4 pr-2">
          {messages.map((message) => {
            const isOwnMessage = message.senderId === account;
            const isFromAgent = message.senderType === 'agent';
            
            return (
              <div
                key={message.id}
                className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`max-w-[70%] ${isOwnMessage ? 'order-2' : 'order-1'}`}>
                  <div
                    className={`rounded-2xl px-4 py-3 ${
                      isOwnMessage
                        ? 'bg-gradient-to-r from-waste-600 to-waste-500 text-white'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'
                    }`}
                  >
                    <p className="text-sm leading-relaxed">{message.content}</p>
                  </div>
                  <div className={`flex items-center mt-1 space-x-2 ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>
                    <div className="flex items-center space-x-1">
                      {isFromAgent ? (
                        <Users className="h-3 w-3 text-blue-500" />
                      ) : (
                        <User className="h-3 w-3 text-green-500" />
                      )}
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {isFromAgent ? 'Agent' : 'User'}
                      </span>
                    </div>
                    <Clock className="h-3 w-3 text-gray-400" />
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {formatTime(message.timestamp)}
                    </span>
                    {isOwnMessage && (
                      <CheckCheck className={`h-3 w-3 ${message.read ? 'text-blue-500' : 'text-gray-400'}`} />
                    )}
                  </div>
                </div>
              </div>
            );
          })}
          <div ref={messagesEndRef} />
        </div>
      </EnhancedCardContent>

      <EnhancedCardFooter className="border-t-0 pt-0">
        <div className="flex space-x-2 w-full">
          <Input
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type your message..."
            className="flex-1 rounded-xl border-2 focus:border-waste-500"
            disabled={isLoading}
          />
          <Button
            onClick={handleSendMessage}
            disabled={!newMessage.trim() || isLoading}
            className="bg-gradient-to-r from-waste-600 to-waste-500 hover:from-waste-700 hover:to-waste-600 text-white rounded-xl px-6"
          >
            {isLoading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </div>
      </EnhancedCardFooter>
    </EnhancedCard>
  );
};

export default MessageThread;
