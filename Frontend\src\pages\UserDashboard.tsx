import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MessageCircle, CheckCircle, Clock, X, Recycle, TrendingUp } from 'lucide-react';
import { PlasticType, WasteReport } from '@/utils/web3Utils';
import { toast } from 'sonner';
import Footer from '@/components/Footer';
import { useContract } from '@/context/ContractContext';
import { useMessages } from '@/context/MessageContext';
import MessageModal from '@/components/MessageModal';
import MessageNotification from '@/components/MessageNotification';
import LoadingSpinner from '@/components/ui/loading-spinner';
import { EnhancedCard, EnhancedCardHeader, EnhancedCardContent, EnhancedCardFooter } from '@/components/ui/enhanced-card';
import * as contracts from '@/utils/contracts';

const UserDashboard: React.FC = () => {
  const [userReports, setUserReports] = useState<WasteReport[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showMessageModal, setShowMessageModal] = useState(false);
  const [selectedReportForMessage, setSelectedReportForMessage] = useState<WasteReport | null>(null);
  
  const { account, tokenBalance } = useContract();
  const { getThreadByReportId, createThread } = useMessages();

  // Fetch user's waste reports
  const fetchUserReports = async () => {
    if (!account) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      // In a real app, this would fetch user-specific reports from blockchain
      const mockReports: WasteReport[] = [
        {
          id: 1,
          reporter: account,
          plasticType: PlasticType.PET,
          quantity: 2,
          location: "40.7128, -74.0060",
          timestamp: Date.now() - ********, // 1 day ago
          status: "pending",
          ipfsHash: "QmTest123",
          rewardEstimate: 2,
          agent: "******************************************"
        },
        {
          id: 2,
          reporter: account,
          plasticType: PlasticType.HDPE,
          quantity: 1,
          location: "40.7589, -73.9851",
          timestamp: Date.now() - *********, // 2 days ago
          status: "collected",
          ipfsHash: "QmTest456",
          rewardEstimate: 1,
          agent: "******************************************"
        },
        {
          id: 3,
          reporter: account,
          plasticType: PlasticType.PVC,
          quantity: 3,
          location: "40.7505, -73.9934",
          timestamp: Date.now() - *********, // 3 days ago
          status: "rejected",
          ipfsHash: "QmTest789",
          rewardEstimate: 3,
          rejectionReason: "Image quality too poor to verify waste type",
          agent: "******************************************"
        }
      ];
      
      setUserReports(mockReports);
    } catch (err) {
      console.error("Error fetching user reports:", err);
      setError("Failed to load your waste reports");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchUserReports();
  }, [account]);

  const getPlasticTypeName = (type: PlasticType): string => {
    return PlasticType[type];
  };

  const formatTimestamp = (timestamp: number): string => {
    const date = new Date(timestamp);
    return `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100';
      case 'collected':
        return 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100';
      case 'rejected':
        return 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-3 w-3 mr-1" />;
      case 'collected':
        return <CheckCircle className="h-3 w-3 mr-1" />;
      case 'rejected':
        return <X className="h-3 w-3 mr-1" />;
      default:
        return null;
    }
  };

  // Message handling functions
  const handleOpenMessage = (report: WasteReport) => {
    setSelectedReportForMessage(report);
    
    // Create thread if it doesn't exist
    const existingThread = getThreadByReportId(report.id);
    if (!existingThread && report.agent) {
      createThread(report.id, report.reporter, report.agent);
    }
    
    setShowMessageModal(true);
  };

  const handleCloseMessage = () => {
    setShowMessageModal(false);
    setSelectedReportForMessage(null);
  };

  const getUnreadMessageCount = (reportId: number): number => {
    const thread = getThreadByReportId(reportId);
    if (!thread) return 0;
    
    // Count unread messages from agents (since this is user dashboard)
    return thread.messages.filter(msg => msg.senderType === 'agent' && !msg.read).length;
  };

  const pendingReports = userReports.filter(report => report.status === 'pending');
  const collectedReports = userReports.filter(report => report.status === 'collected');
  const rejectedReports = userReports.filter(report => report.status === 'rejected');

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-waste-50 dark:from-gray-900 dark:via-gray-800 dark:to-waste-900">
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8">
        {/* Header Section */}
        <div className="mb-12">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-6">
            <div className="animate-fade-in">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-waste-600 to-waste-400 rounded-xl flex items-center justify-center">
                  <Recycle className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl font-extrabold gradient-text sm:text-5xl">
                    My Dashboard
                  </h1>
                  <div className="flex items-center space-x-2 mt-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-sm text-gray-500 dark:text-gray-400 font-medium">Active User</span>
                  </div>
                </div>
              </div>
              <p className="max-w-2xl text-xl text-gray-600 dark:text-gray-300 leading-relaxed">
                Track your waste reports, communicate with agents, and monitor your environmental impact.
              </p>
            </div>
            
            {/* Quick Stats */}
            <div className="grid grid-cols-3 gap-4 lg:min-w-[300px] animate-fade-in" style={{animationDelay: '0.2s'}}>
              <EnhancedCard variant="glass" padding="sm" className="text-center">
                <div className="text-2xl font-bold gradient-text">{pendingReports.length}</div>
                <div className="text-xs text-gray-500 dark:text-gray-400 font-medium">Pending</div>
              </EnhancedCard>
              <EnhancedCard variant="glass" padding="sm" className="text-center">
                <div className="text-2xl font-bold text-green-600 dark:text-green-400">{collectedReports.length}</div>
                <div className="text-xs text-gray-500 dark:text-gray-400 font-medium">Collected</div>
              </EnhancedCard>
              <EnhancedCard variant="glass" padding="sm" className="text-center">
                <div className="text-2xl font-bold gradient-text">{tokenBalance}</div>
                <div className="text-xs text-gray-500 dark:text-gray-400 font-medium">WVT Earned</div>
              </EnhancedCard>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="mt-8 flex flex-wrap gap-3 animate-fade-in" style={{animationDelay: '0.4s'}}>
            <Button
              onClick={fetchUserReports}
              disabled={isLoading}
              className="bg-gradient-to-r from-waste-600 to-waste-500 hover:from-waste-700 hover:to-waste-600 text-white shadow-lg hover:shadow-xl transition-all duration-300"
            >
              {isLoading ? (
                <>🔄 Refreshing...</>
              ) : (
                <><TrendingUp className="h-4 w-4 mr-2" /> Refresh Reports</>
              )}
            </Button>
          </div>
        </div>

        {/* Content */}
        {isLoading ? (
          <div className="flex flex-col items-center justify-center py-20">
            <LoadingSpinner 
              size="xl" 
              variant="dots" 
              text="Loading your waste reports..." 
              className="animate-fade-in"
            />
          </div>
        ) : error ? (
          <EnhancedCard variant="glass" className="text-center py-12 border-red-200 dark:border-red-800">
            <div className="text-6xl mb-4">⚠️</div>
            <h3 className="text-xl font-semibold text-red-600 dark:text-red-400 mb-2">Error Loading Reports</h3>
            <p className="text-red-600 dark:text-red-400 mb-6">{error}</p>
            <Button
              onClick={fetchUserReports}
              className="bg-red-600 hover:bg-red-700 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            >
              🔄 Retry
            </Button>
          </EnhancedCard>
        ) : userReports.length === 0 ? (
          <EnhancedCard variant="glass" className="text-center py-20">
            <div className="text-6xl mb-4">📋</div>
            <h3 className="text-xl font-semibold text-gray-600 dark:text-gray-400 mb-2">No Reports Yet</h3>
            <p className="text-gray-500 dark:text-gray-500 mb-6">Start by reporting your first waste item!</p>
            <Button
              onClick={() => window.location.href = '/report-waste'}
              className="bg-gradient-to-r from-waste-600 to-waste-500 hover:from-waste-700 hover:to-waste-600 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            >
              🗑️ Report Waste
            </Button>
          </EnhancedCard>
        ) : (
          <div className="space-y-10">
            {/* Pending Reports */}
            {pendingReports.length > 0 && (
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                  Pending Reports
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {pendingReports.map((report) => (
                    <Card key={report.id} className="overflow-hidden floating-card">
                      <CardHeader className="bg-yellow-50 dark:bg-yellow-900/20">
                        <div className="flex justify-between items-start">
                          <div>
                            <CardTitle>Report #{report.id}</CardTitle>
                            <CardDescription>
                              {formatTimestamp(report.timestamp)}
                            </CardDescription>
                          </div>
                          <div className="flex items-center space-x-2">
                            {report.agent && (
                              <MessageNotification
                                unreadCount={getUnreadMessageCount(report.id)}
                                onClick={() => handleOpenMessage(report)}
                                className="text-waste-600 hover:text-waste-700"
                              />
                            )}
                            <Badge className={getStatusColor(report.status)}>
                              {getStatusIcon(report.status)} {report.status}
                            </Badge>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-4">
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-500 dark:text-gray-400">Plastic Type:</span>
                            <span className="font-medium">{getPlasticTypeName(report.plasticType)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-500 dark:text-gray-400">Quantity:</span>
                            <span className="font-medium">{report.quantity} kg</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-500 dark:text-gray-400">Expected Reward:</span>
                            <span className="font-medium text-waste-600 dark:text-waste-400">
                              {report.rewardEstimate} WVT
                            </span>
                          </div>
                        </div>
                        {report.agent && (
                          <div className="mt-4">
                            <Button
                              onClick={() => handleOpenMessage(report)}
                              variant="outline"
                              className="w-full border-waste-300 text-waste-700 hover:bg-waste-50 dark:border-waste-600 dark:text-waste-300 dark:hover:bg-waste-900/20"
                            >
                              💬 Message Agent
                              {getUnreadMessageCount(report.id) > 0 && (
                                <Badge className="ml-2 bg-red-500 text-white text-xs">
                                  {getUnreadMessageCount(report.id)}
                                </Badge>
                              )}
                            </Button>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {/* Other sections would continue here... */}
          </div>
        )}
      </div>

      {/* Message Modal */}
      {showMessageModal && selectedReportForMessage && (
        <MessageModal
          isOpen={showMessageModal}
          onClose={handleCloseMessage}
          reportId={selectedReportForMessage.id}
          reporterAddress={selectedReportForMessage.reporter}
          agentAddress={selectedReportForMessage.agent}
          isAgent={false}
        />
      )}

      <Footer />
    </div>
  );
};

export default UserDashboard;
