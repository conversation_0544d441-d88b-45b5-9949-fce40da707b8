import React from 'react';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { MessageCircle, Bell } from 'lucide-react';

interface MessageNotificationProps {
  unreadCount: number;
  onClick: () => void;
  className?: string;
}

const MessageNotification: React.FC<MessageNotificationProps> = ({
  unreadCount,
  onClick,
  className = ''
}) => {
  if (unreadCount === 0) {
    return (
      <Button
        variant="ghost"
        size="sm"
        onClick={onClick}
        className={`relative ${className}`}
      >
        <MessageCircle className="h-4 w-4" />
      </Button>
    );
  }

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={onClick}
      className={`relative ${className} animate-pulse`}
    >
      <MessageCircle className="h-4 w-4" />
      <Badge 
        className="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-red-500 text-white text-xs flex items-center justify-center p-0 animate-bounce"
      >
        {unreadCount > 9 ? '9+' : unreadCount}
      </Badge>
    </Button>
  );
};

export default MessageNotification;
