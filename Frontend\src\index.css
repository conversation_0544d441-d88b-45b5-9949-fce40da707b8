
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 160 84% 39%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 164 74% 73%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 164 74% 73%;

    --radius: 0.5rem;

    --sidebar-background: 160 84% 39%;
    --sidebar-foreground: 0 0% 100%;
    --sidebar-primary: 164 74% 73%;
    --sidebar-primary-foreground: 222.2 47.4% 11.2%;
    --sidebar-accent: 165 54% 49%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 165 54% 59%;
    --sidebar-ring: 165 54% 69%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 164 74% 63%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 164 74% 53%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 164 74% 53%;

    --sidebar-background: 164 74% 23%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 164 74% 53%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 165 54% 39%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 165 54% 29%;
    --sidebar-ring: 165 54% 49%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .waste-gradient {
    @apply bg-gradient-to-br from-waste-300 to-waste-600;
  }

  .waste-gradient-subtle {
    @apply bg-gradient-to-br from-waste-50 via-waste-100 to-waste-200 dark:from-waste-900 dark:via-waste-800 dark:to-waste-700;
  }

  .eco-section {
    @apply py-16 px-4 md:px-8;
  }

  .section-heading {
    @apply text-3xl md:text-4xl font-bold mb-6 text-waste-800 dark:text-waste-200;
  }

  .section-subheading {
    @apply text-xl md:text-2xl font-semibold mb-4 text-waste-700 dark:text-waste-300;
  }

  .glass-card {
    @apply bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-white/20 dark:border-gray-700/20 shadow-xl;
  }

  .floating-card {
    @apply transform transition-all duration-300 hover:scale-105 hover:shadow-2xl;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-waste-600 to-waste-400 bg-clip-text text-transparent;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-bounce-slow {
    animation: bounce 2s infinite;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out forwards;
}

@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
}
