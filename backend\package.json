{"name": "wastevan-eco-connect-backend", "version": "1.0.0", "description": "Backend for WasteVan Eco-Connect application", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "mongoose": "^8.0.0"}, "devDependencies": {"nodemon": "^3.0.1"}}