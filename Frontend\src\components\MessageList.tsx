import React from 'react';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Enhanced<PERSON>ard, EnhancedCardHeader, EnhancedCardContent } from './ui/enhanced-card';
import { MessageCircle, User, Users, Clock } from 'lucide-react';
import { useMessages } from '../context/MessageContext';
import { useContract } from '../context/ContractContext';

interface MessageListProps {
  onSelectThread: (reportId: number, reporterAddress: string, agentAddress?: string) => void;
  onClose: () => void;
}

const MessageList: React.FC<MessageListProps> = ({ onSelectThread, onClose }) => {
  const { threads } = useMessages();
  const { isAgent } = useContract();

  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const minutes = Math.floor(diffInHours * 60);
      return `${minutes}m ago`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const getUnreadCount = (thread: any) => {
    if (isAgent) {
      return thread.messages.filter((msg: any) => msg.senderType === 'user' && !msg.read).length;
    } else {
      return thread.messages.filter((msg: any) => msg.senderType === 'agent' && !msg.read).length;
    }
  };

  const getLastMessage = (thread: any) => {
    const lastMessage = thread.messages[thread.messages.length - 1];
    if (!lastMessage) return 'No messages yet';
    
    const preview = lastMessage.content.length > 50 
      ? lastMessage.content.substring(0, 50) + '...' 
      : lastMessage.content;
    
    return preview;
  };

  const getOtherPartyAddress = (thread: any) => {
    if (isAgent) {
      return thread.reporterAddress;
    } else {
      return thread.agentAddress || 'Agent';
    }
  };

  const sortedThreads = threads.sort((a, b) => b.lastActivity - a.lastActivity);

  return (
    <EnhancedCard variant="glass" className="h-[600px] flex flex-col">
      <EnhancedCardHeader 
        icon={<MessageCircle className="h-5 w-5 text-waste-600" />}
        badge={
          <Button variant="ghost" size="sm" onClick={onClose} className="text-gray-500 hover:text-gray-700">
            ✕
          </Button>
        }
      >
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Messages
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {threads.length} conversation{threads.length !== 1 ? 's' : ''}
          </p>
        </div>
      </EnhancedCardHeader>

      <EnhancedCardContent className="flex-1 overflow-hidden">
        {threads.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <div className="text-6xl mb-4">💬</div>
            <h4 className="text-lg font-semibold text-gray-600 dark:text-gray-400 mb-2">
              No Messages Yet
            </h4>
            <p className="text-gray-500 dark:text-gray-500 text-sm">
              {isAgent 
                ? 'Start collecting waste to communicate with users'
                : 'Report waste to start communicating with agents'
              }
            </p>
          </div>
        ) : (
          <div className="h-full overflow-y-auto space-y-3">
            {sortedThreads.map((thread) => {
              const unreadCount = getUnreadCount(thread);
              const otherParty = getOtherPartyAddress(thread);
              
              return (
                <div
                  key={thread.reportId}
                  className="p-4 rounded-xl border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer transition-all duration-200 hover:shadow-md"
                  onClick={() => onSelectThread(thread.reportId, thread.reporterAddress, thread.agentAddress)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 flex-1">
                      <div className="flex-shrink-0">
                        {isAgent ? (
                          <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                            <User className="h-5 w-5 text-green-600 dark:text-green-400" />
                          </div>
                        ) : (
                          <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                            <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                          </div>
                        )}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h4 className="text-sm font-semibold text-gray-900 dark:text-white truncate">
                            Report #{thread.reportId}
                          </h4>
                          <div className="flex items-center space-x-2">
                            {unreadCount > 0 && (
                              <Badge className="bg-red-500 text-white text-xs">
                                {unreadCount}
                              </Badge>
                            )}
                            <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                              <Clock className="h-3 w-3 mr-1" />
                              {formatTime(thread.lastActivity)}
                            </div>
                          </div>
                        </div>
                        
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          {isAgent ? 'User' : 'Agent'}: {otherParty.substring(0, 6)}...{otherParty.substring(otherParty.length - 4)}
                        </p>
                        
                        <p className="text-sm text-gray-600 dark:text-gray-300 mt-2 truncate">
                          {getLastMessage(thread)}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </EnhancedCardContent>
    </EnhancedCard>
  );
};

export default MessageList;
