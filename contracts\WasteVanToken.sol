// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

contract WasteVanToken is ERC20, Ownable {
    mapping(address => bool) public minters;

    event MinterAdded(address indexed minter);
    event MinterRemoved(address indexed minter);

    modifier onlyMinter() {
        require(minters[msg.sender] || msg.sender == owner(), "Not authorized to mint");
        _;
    }

    constructor() ERC20("WasteVan Token", "WVT") Ownable() {
        // Initial supply of 1 million tokens
        _mint(msg.sender, 1000000 * 10 ** decimals());
    }

    function addMinter(address minter) public onlyOwner {
        minters[minter] = true;
        emit MinterAdded(minter);
    }

    function removeMinter(address minter) public onlyOwner {
        minters[minter] = false;
        emit MinterRemoved(minter);
    }

    function mint(address to, uint256 amount) public onlyMinter {
        _mint(to, amount);
    }

    function burn(uint256 amount) public {
        _burn(msg.sender, amount);
    }

    // Allow authorized minters to transfer tokens on behalf of users (for reward distribution)
    function transferFromAgent(address from, address to, uint256 amount) public onlyMinter returns (bool) {
        require(balanceOf(from) >= amount, "Insufficient balance");
        _transfer(from, to, amount);
        return true;
    }
}