import React from 'react';
import {
  Di<PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from './ui/dialog';
import MessageThread from './MessageThread';

interface MessageModalProps {
  isOpen: boolean;
  onClose: () => void;
  reportId: number;
  reporterAddress: string;
  agentAddress?: string;
  isAgent?: boolean;
}

const MessageModal: React.FC<MessageModalProps> = ({
  isOpen,
  onClose,
  reportId,
  reporterAddress,
  agentAddress,
  isAgent = false
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] p-0 overflow-hidden">
        <MessageThread
          reportId={reportId}
          reporterAddress={reporterAddress}
          agentAddress={agentAddress}
          onClose={onClose}
          isAgent={isAgent}
        />
      </DialogContent>
    </Dialog>
  );
};

export default MessageModal;
